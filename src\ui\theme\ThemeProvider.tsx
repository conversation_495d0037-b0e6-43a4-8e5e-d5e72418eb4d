/**
 * Theme Provider - 主题提供者
 * 
 * 提供主题上下文和主题切换功能
 */

import React, { createContext, useContext, useEffect, useState } from 'react';
import { Theme } from '@/types';
import { ThemeConfig } from '../types';
import { lightTheme, darkTheme, highContrastTheme } from './themes';

interface ThemeContextType {
  theme: Theme;
  themeConfig: ThemeConfig;
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: Theme;
  storageKey?: string;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({
  children,
  defaultTheme = 'dark',
  storageKey = 'ai-agent-theme',
}) => {
  const [theme, setThemeState] = useState<Theme>(defaultTheme);

  // 主题配置映射
  const themeConfigs: Record<Theme, ThemeConfig> = {
    light: lightTheme,
    dark: darkTheme,
    'high-contrast': highContrastTheme,
  };

  // 从本地存储加载主题
  useEffect(() => {
    try {
      const savedTheme = localStorage.getItem(storageKey) as Theme;
      if (savedTheme && themeConfigs[savedTheme]) {
        setThemeState(savedTheme);
      }
    } catch (error) {
      console.warn('Failed to load theme from localStorage:', error);
    }
  }, [storageKey]);

  // 应用主题到文档
  useEffect(() => {
    const config = themeConfigs[theme];
    const root = document.documentElement;

    // 设置CSS变量
    Object.entries(config.colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key}`, value);
    });

    Object.entries(config.spacing).forEach(([key, value]) => {
      root.style.setProperty(`--spacing-${key}`, value);
    });

    Object.entries(config.typography.fontSize).forEach(([key, value]) => {
      root.style.setProperty(`--font-size-${key}`, value);
    });

    Object.entries(config.typography.fontWeight).forEach(([key, value]) => {
      root.style.setProperty(`--font-weight-${key}`, value.toString());
    });

    Object.entries(config.borderRadius).forEach(([key, value]) => {
      root.style.setProperty(`--border-radius-${key}`, value);
    });

    Object.entries(config.shadows).forEach(([key, value]) => {
      root.style.setProperty(`--shadow-${key}`, value);
    });

    // 设置基础样式
    root.style.setProperty('--font-family', config.typography.fontFamily);
    root.style.setProperty('--theme-name', theme);

    // 添加主题类名
    document.body.className = document.body.className
      .replace(/theme-\w+/g, '')
      .trim();
    document.body.classList.add(`theme-${theme}`);
  }, [theme, themeConfigs]);

  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme);
    try {
      localStorage.setItem(storageKey, newTheme);
    } catch (error) {
      console.warn('Failed to save theme to localStorage:', error);
    }
  };

  const toggleTheme = () => {
    const themes: Theme[] = ['light', 'dark', 'high-contrast'];
    const currentIndex = themes.indexOf(theme);
    const nextIndex = (currentIndex + 1) % themes.length;
    setTheme(themes[nextIndex]);
  };

  const value: ThemeContextType = {
    theme,
    themeConfig: themeConfigs[theme],
    setTheme,
    toggleTheme,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

// 主题切换钩子
export const useThemeToggle = () => {
  const { theme, setTheme, toggleTheme } = useTheme();
  
  return {
    theme,
    setTheme,
    toggleTheme,
    isLight: theme === 'light',
    isDark: theme === 'dark',
    isHighContrast: theme === 'high-contrast',
  };
};

// CSS-in-JS 样式钩子
export const useThemeStyles = () => {
  const { themeConfig } = useTheme();
  
  return {
    colors: themeConfig.colors,
    spacing: themeConfig.spacing,
    typography: themeConfig.typography,
    borderRadius: themeConfig.borderRadius,
    shadows: themeConfig.shadows,
  };
};

// 响应式主题钩子
export const useResponsiveTheme = () => {
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  const { themeConfig } = useTheme();

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768);
      setIsTablet(window.innerWidth >= 768 && window.innerWidth < 1024);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  return {
    isMobile,
    isTablet,
    isDesktop: !isMobile && !isTablet,
    themeConfig,
    spacing: {
      xs: isMobile ? '4px' : themeConfig.spacing.xs,
      sm: isMobile ? '8px' : themeConfig.spacing.sm,
      md: isMobile ? '12px' : themeConfig.spacing.md,
      lg: isMobile ? '16px' : themeConfig.spacing.lg,
      xl: isMobile ? '20px' : themeConfig.spacing.xl,
    },
    fontSize: {
      xs: isMobile ? '12px' : themeConfig.typography.fontSize.xs,
      sm: isMobile ? '14px' : themeConfig.typography.fontSize.sm,
      md: isMobile ? '16px' : themeConfig.typography.fontSize.md,
      lg: isMobile ? '18px' : themeConfig.typography.fontSize.lg,
      xl: isMobile ? '20px' : themeConfig.typography.fontSize.xl,
    },
  };
};
