/**
 * UI Module Exports
 * 
 * 导出UI模块的所有公共组件和工具
 */

// 主题相关
export { ThemeProvider, useTheme, useThemeToggle, useThemeStyles, useResponsiveTheme } from './theme/ThemeProvider';
export { lightTheme, darkTheme, highContrastTheme, getThemeConfig, generateCSSVariables, detectSystemTheme } from './theme/themes';

// 基础组件
export { Button, ButtonGroup, IconButton, FloatingActionButton, ToggleButton, LinkButton } from './components/Button';
export { Message, MessageList } from './components/Message';
export { CodeBlock, InlineCode, CodeDiff } from './components/CodeBlock';
export { ChatInput, QuickReply, FileUpload, VoiceInput } from './components/ChatInput';

// 类型定义
export type {
  ThemeColors,
  ThemeConfig,
  MessageProps,
  CodeBlockProps,
  ChatProps,
  ChatInputProps,
  SidebarProps,
  SidebarItemProps,
  StatusBarProps,
  SettingsProps,
  SettingsData,
  ToolbarProps,
  LoadingProps,
  SkeletonProps,
  ModalProps,
  NotificationProps,
  NotificationContextType,
  KeyboardShortcut,
  DragDropProps,
  VirtualListProps,
  SearchProps,
  TabProps,
  TabsProps,
  ContextMenuItem,
  ContextMenuProps,
  Breakpoints,
  ResponsiveProps,
  AnimationProps,
  FormFieldProps,
  InputProps,
  ButtonProps,
  LayoutProps,
  GridProps,
  FlexProps,
} from './types';

// 工厂函数
export function createThemeProvider(defaultTheme?: any) {
  const { ThemeProvider: Provider } = require('./theme/ThemeProvider');
  return function(props: any) {
    return Provider({ ...props, defaultTheme });
  };
}
